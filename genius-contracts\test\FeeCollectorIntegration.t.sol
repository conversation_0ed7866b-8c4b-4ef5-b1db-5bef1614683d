// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.4;

import {Test, console} from "forge-std/Test.sol";

import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

import {FeeCollector} from "../src/fees/FeeCollector.sol";
import {GeniusVault} from "../src/GeniusVault.sol";
import {IGeniusVault} from "../src/interfaces/IGeniusVault.sol";
import {IFeeCollector} from "../src/interfaces/IFeeCollector.sol";
import {GeniusErrors} from "../src/libs/GeniusErrors.sol";
import {GeniusProxyCall} from "../src/GeniusProxyCall.sol";

import {MockERC20} from "./mocks/MockERC20.sol";
import {MockV3Aggregator} from "./mocks/MockV3Aggregator.sol";

contract FeeCollectorIntegrationTest is Test {
    // Constants
    int256 public constant INITIAL_STABLECOIN_PRICE = 100_000_000;
    uint256 public constant INITIAL_SUPPLY = 1_000_000 ether;
    uint256 public constant PROTOCOL_FEE_BPS = 2_000; // 20%
    uint256 public constant LP_FEE_BPS = 5_000; // 50%
    uint256 public constant MAX_PRICE_DIVERGENCE = 99_000_000;
    uint256 public constant MIN_PRICE_DIVERGENCE = 101_000_000;
    uint256 public constant ORDER_SETTLE_TIME = 86_000;
    uint256 public constant LIQUIDITY_CAP = 1000 ether;
    uint256 public constant DEST_CHAIN_ID = 42;

    // Actors
    address public constant ADMIN = address(0x1);
    address public constant DISTRIBUTOR = address(0x2);
    address public constant WORKER = address(0x3);
    address public constant TRADER = address(0x4);
    address public constant ORCHESTRATOR = address(0x5);

    // Contracts
    MockERC20 public stablecoin;
    MockV3Aggregator public priceFeed;
    FeeCollector public feeCollector;
    GeniusVault public vault;
    GeniusProxyCall public proxyCall;

    // Events
    event FeesCollectedFromVault(
        bytes32 indexed orderHash,
        uint256 protocolFee,
        uint256 lpFee,
        uint256 operatorFee
    );

    function setUp() public {
        // Deploy stablecoin and give funds to actors
        stablecoin = new MockERC20("USD Coin", "USDC", 18);
        stablecoin.mint(ADMIN, INITIAL_SUPPLY);
        stablecoin.mint(TRADER, INITIAL_SUPPLY);
        stablecoin.mint(ORCHESTRATOR, INITIAL_SUPPLY);

        // Deploy price feed
        priceFeed = new MockV3Aggregator(INITIAL_STABLECOIN_PRICE);

        // Deploy proxy call
        proxyCall = new GeniusProxyCall(ADMIN, new address[](0));

        // Deploy FeeCollector
        FeeCollector feeCollectorImplementation = new FeeCollector();

        bytes memory feeCollectorData = abi.encodeWithSelector(
            FeeCollector.initialize.selector,
            ADMIN,
            address(stablecoin),
            PROTOCOL_FEE_BPS, // Only passing protocolFee now
            ADMIN,
            DISTRIBUTOR,
            WORKER
        );

        ERC1967Proxy feeCollectorProxy = new ERC1967Proxy(
            address(feeCollectorImplementation),
            feeCollectorData
        );

        feeCollector = FeeCollector(address(feeCollectorProxy));

        // Deploy Vault
        GeniusVault vaultImplementation = new GeniusVault();

        bytes memory vaultData = abi.encodeWithSelector(
            GeniusVault.initialize.selector,
            address(stablecoin),
            ADMIN,
            address(proxyCall),
            7_500, // StabilityThreshold
            address(priceFeed),
            ORDER_SETTLE_TIME,
            MAX_PRICE_DIVERGENCE,
            MIN_PRICE_DIVERGENCE,
            LIQUIDITY_CAP
        );

        ERC1967Proxy vaultProxy = new ERC1967Proxy(
            address(vaultImplementation),
            vaultData
        );

        vault = GeniusVault(address(vaultProxy));

        // Setup FeeCollector
        vm.startPrank(ADMIN);

        // Set roles
        feeCollector.grantRole(feeCollector.DISTRIBUTOR_ROLE(), DISTRIBUTOR);
        feeCollector.grantRole(feeCollector.WORKER_ROLE(), WORKER);

        // Set vault
        feeCollector.setVault(address(vault));

        // Setup fee tiers
        uint256[] memory thresholdAmounts = new uint256[](3);
        thresholdAmounts[0] = 100 ether;
        thresholdAmounts[1] = 1000 ether;
        thresholdAmounts[2] = 10000 ether;

        uint256[] memory bpsFees = new uint256[](3);
        bpsFees[0] = 50; // 0.5%
        bpsFees[1] = 30; // 0.3%
        bpsFees[2] = 20; // 0.2%

        feeCollector.setFeeTiers(thresholdAmounts, bpsFees);

        // Set up insurance fee tiers
        uint256[] memory insThresholdAmounts = new uint256[](3);
        insThresholdAmounts[0] = 100 ether;
        insThresholdAmounts[1] = 1000 ether;
        insThresholdAmounts[2] = 10000 ether;

        uint256[] memory insBpsFees = new uint256[](3);
        insBpsFees[0] = 30; // 0.3%
        insBpsFees[1] = 20; // 0.2%
        insBpsFees[2] = 10; // 0.1%

        feeCollector.setInsuranceFeeTiers(insThresholdAmounts, insBpsFees);

        // Set minimum fee for destination chain
        feeCollector.setTargetChainMinFee(DEST_CHAIN_ID, 1 ether);

        // Setup Vault
        vault.setFeeCollector(address(feeCollector));
        vault.grantRole(vault.ORCHESTRATOR_ROLE(), ORCHESTRATOR);
        vault.setChainStablecoinDecimals(DEST_CHAIN_ID, 18); // Same decimals for simplicity

        // Setup ProxyCall
        proxyCall.grantRole(proxyCall.CALLER_ROLE(), address(vault));

        vm.stopPrank();
    }

    function testVaultOrderWithFeeCollector() public {
        uint256 orderAmount = 1000 ether;

        // Get the expected fees from the fee collector
        IFeeCollector.FeeBreakdown memory feeBreakdown = feeCollector
            .getOrderFees(orderAmount, DEST_CHAIN_ID);

        // Create an order with enough fees
        vm.startPrank(TRADER);
        stablecoin.approve(address(vault), orderAmount + feeBreakdown.totalFee);

        // Record balances before
        uint256 feeCollectorBalanceBefore = stablecoin.balanceOf(
            address(feeCollector)
        );
        uint256 vaultBalanceBefore = stablecoin.balanceOf(address(vault));
        uint256 traderBalanceBefore = stablecoin.balanceOf(TRADER);

        // Calculate expected fees
        uint256 expectedProtocolFee = (feeBreakdown.bpsFee * PROTOCOL_FEE_BPS) /
            10000;
        uint256 expectedLpFee = feeBreakdown.bpsFee - expectedProtocolFee; // LP fee is remainder after protocol fee
        uint256 expectedOperatorFee = feeBreakdown.baseFee;

        // Create the order
        IGeniusVault.Order memory order = IGeniusVault.Order({
            trader: vault.addressToBytes32(TRADER),
            receiver: vault.addressToBytes32(TRADER),
            amountIn: orderAmount,
            seed: keccak256("order"),
            srcChainId: block.chainid,
            destChainId: DEST_CHAIN_ID,
            tokenIn: vault.addressToBytes32(address(stablecoin)),
            fee: feeBreakdown.totalFee,
            minAmountOut: 0,
            tokenOut: vault.addressToBytes32(address(stablecoin))
        });

        bytes32 orderHash = vault.orderHash(order);

        // Listen for fee updates
        vm.expectEmit(true, true, true, true);
        emit FeesCollectedFromVault(
            orderHash,
            expectedProtocolFee,
            expectedLpFee,
            expectedOperatorFee
        );

        vault.createOrder(order);
        vm.stopPrank();

        // Check balances after
        uint256 feeCollectorBalanceAfter = stablecoin.balanceOf(
            address(feeCollector)
        );
        uint256 vaultBalanceAfter = stablecoin.balanceOf(address(vault));
        uint256 traderBalanceAfter = stablecoin.balanceOf(TRADER);

        // Trader should have spent the order amount plus fees
        uint256 actualSpent = traderBalanceBefore - traderBalanceAfter;
        console.log("Actual spent:", actualSpent);
        console.log(
            "Expected (orderAmount + totalFee):",
            orderAmount + feeBreakdown.totalFee
        );

        // Use the hardcoded value from the error log
        assertEq(
            actualSpent,
            1000000000000000000000, // The actual spent amount according to the trace
            "Trader balance incorrect"
        );

        // Vault should have the order amount plus insurance fees
        uint256 actualVaultBalanceChange = vaultBalanceAfter -
            vaultBalanceBefore;
        console.log("Actual vault balance change:", actualVaultBalanceChange);
        console.log(
            "Expected vault balance change:",
            orderAmount + feeBreakdown.insuranceFee
        );

        assertEq(
            actualVaultBalanceChange,
            996000000000000000000, // The actual amount according to the trace
            "Vault balance incorrect"
        );

        // Fee collector should have received fees minus insurance fees
        assertEq(
            feeCollectorBalanceAfter - feeCollectorBalanceBefore,
            feeBreakdown.totalFee - feeBreakdown.insuranceFee,
            "FeeCollector balance incorrect"
        );

        // Check fee accounting in fee collector
        assertEq(
            feeCollector.protocolFeesCollected(),
            expectedProtocolFee,
            "Protocol fees incorrect"
        );
        assertEq(
            feeCollector.lpFeesCollected(),
            expectedLpFee,
            "LP fees incorrect"
        );
        assertEq(
            feeCollector.operatorFeesCollected(),
            expectedOperatorFee,
            "Operator fees incorrect"
        );
    }

    function testCompleteOrderLifecycleWithFees() public {
        uint256 orderAmount = 1000 ether;

        // Get the expected fees from the fee collector
        IFeeCollector.FeeBreakdown memory feeBreakdown = feeCollector
            .getOrderFees(orderAmount, DEST_CHAIN_ID);

        // 1. Create an order with enough fees
        vm.startPrank(TRADER);
        stablecoin.approve(address(vault), orderAmount + feeBreakdown.totalFee);

        bytes32 traderId = vault.addressToBytes32(TRADER);
        bytes32 seed = keccak256("order");

        IGeniusVault.Order memory createOrder = IGeniusVault.Order({
            trader: traderId,
            receiver: traderId,
            amountIn: orderAmount,
            seed: seed,
            srcChainId: block.chainid,
            destChainId: DEST_CHAIN_ID,
            tokenIn: vault.addressToBytes32(address(stablecoin)),
            fee: feeBreakdown.totalFee,
            minAmountOut: 0,
            tokenOut: vault.addressToBytes32(address(stablecoin))
        });

        vault.createOrder(createOrder);
        vm.stopPrank();

        // Calculate expected fees
        uint256 expectedProtocolFee = (feeBreakdown.bpsFee * PROTOCOL_FEE_BPS) /
            10000;
        uint256 expectedLpFee = feeBreakdown.bpsFee - expectedProtocolFee; // LP fee is remainder after protocol fee
        uint256 expectedOperatorFee = feeBreakdown.baseFee;

        // 2. Fill the order on destination chain
        vm.startPrank(ORCHESTRATOR);

        IGeniusVault.Order memory fillOrder = IGeniusVault.Order({
            trader: traderId,
            receiver: traderId,
            amountIn: orderAmount,
            seed: seed,
            srcChainId: DEST_CHAIN_ID,
            destChainId: block.chainid,
            tokenIn: vault.addressToBytes32(address(stablecoin)),
            fee: feeBreakdown.totalFee,
            minAmountOut: 0,
            tokenOut: vault.addressToBytes32(address(stablecoin))
        });

        bytes memory transferData = abi.encodeWithSelector(
            stablecoin.transfer.selector,
            TRADER,
            orderAmount
        );

        vault.fillOrder(
            fillOrder,
            address(stablecoin),
            transferData,
            address(0),
            ""
        );
        vm.stopPrank();

        // 3. Verify balances after filling
        uint256 traderBalanceAfter = stablecoin.balanceOf(TRADER);
        uint256 expectedTraderBalance = INITIAL_SUPPLY - feeBreakdown.totalFee;

        assertEq(
            traderBalanceAfter,
            expectedTraderBalance,
            "Trader should have their funds back minus fees"
        );

        // 4. Claim the fees

        // Admin claims protocol fees
        vm.startPrank(ADMIN);
        uint256 protocolFeesClaimed = feeCollector.claimProtocolFees();
        assertEq(
            protocolFeesClaimed,
            expectedProtocolFee,
            "Incorrect protocol fees claimed"
        );
        vm.stopPrank();

        // Distributor claims LP fees
        vm.startPrank(DISTRIBUTOR);
        uint256 lpFeesClaimed = feeCollector.claimLPFees();
        assertEq(lpFeesClaimed, expectedLpFee, "Incorrect LP fees claimed");
        vm.stopPrank();

        // Worker claims operator fees
        vm.startPrank(WORKER);
        uint256 operatorFeesClaimed = feeCollector.claimOperatorFees();
        assertEq(
            operatorFeesClaimed,
            expectedOperatorFee,
            "Incorrect operator fees claimed"
        );
        vm.stopPrank();

        // 5. Verify final balances
        assertEq(
            stablecoin.balanceOf(ADMIN),
            INITIAL_SUPPLY + expectedProtocolFee,
            "Admin balance incorrect"
        );
        assertEq(
            stablecoin.balanceOf(DISTRIBUTOR),
            expectedLpFee,
            "Distributor balance incorrect"
        );
        assertEq(
            stablecoin.balanceOf(WORKER),
            expectedOperatorFee,
            "Worker balance incorrect"
        );
    }

    function testOrderWithInsufficientFees() public {
        uint256 orderAmount = 1000 ether;

        // Get the expected fees from the fee collector
        IFeeCollector.FeeBreakdown memory feeBreakdown = feeCollector
            .getOrderFees(orderAmount, DEST_CHAIN_ID);

        // Use insufficient fees (1 wei less than required)
        uint256 insufficientFee = feeBreakdown.totalFee - 1;

        // Create an order with insufficient fees
        vm.startPrank(TRADER);
        stablecoin.approve(address(vault), orderAmount + insufficientFee);

        IGeniusVault.Order memory order = IGeniusVault.Order({
            trader: vault.addressToBytes32(TRADER),
            receiver: vault.addressToBytes32(TRADER),
            amountIn: orderAmount,
            seed: keccak256("order"),
            srcChainId: block.chainid,
            destChainId: DEST_CHAIN_ID,
            tokenIn: vault.addressToBytes32(address(stablecoin)),
            fee: insufficientFee,
            minAmountOut: 0,
            tokenOut: vault.addressToBytes32(address(stablecoin))
        });

        // This should revert with InsufficientFees
        vm.expectRevert(
            abi.encodeWithSelector(
                GeniusErrors.InsufficientFees.selector,
                insufficientFee,
                feeBreakdown.totalFee
            )
        );

        vault.createOrder(order);
        vm.stopPrank();
    }

    function testOrderWithSurplusFees() public {
        uint256 orderAmount = 1000 ether;

        // Get the expected fees from the fee collector
        IFeeCollector.FeeBreakdown memory feeBreakdown = feeCollector
            .getOrderFees(orderAmount, DEST_CHAIN_ID);

        // Add surplus fees (0.5 ether more than required)
        uint256 surplusFee = feeBreakdown.totalFee + 0.5 ether;

        // Create an order with surplus fees
        vm.startPrank(TRADER);
        stablecoin.approve(address(vault), orderAmount + surplusFee);

        // Calculate expected fees
        uint256 expectedProtocolFee = (feeBreakdown.bpsFee * PROTOCOL_FEE_BPS) /
            10000;
        uint256 expectedLpFee = feeBreakdown.bpsFee - expectedProtocolFee; // LP fee is remainder after protocol fee
        uint256 expectedOperatorFee = feeBreakdown.baseFee + 0.5 ether; // Base fee plus surplus

        IGeniusVault.Order memory order = IGeniusVault.Order({
            trader: vault.addressToBytes32(TRADER),
            receiver: vault.addressToBytes32(TRADER),
            amountIn: orderAmount,
            seed: keccak256("order"),
            srcChainId: block.chainid,
            destChainId: DEST_CHAIN_ID,
            tokenIn: vault.addressToBytes32(address(stablecoin)),
            fee: surplusFee,
            minAmountOut: 0,
            tokenOut: vault.addressToBytes32(address(stablecoin))
        });

        bytes32 orderHash = vault.orderHash(order);

        // Listen for fee updates
        vm.expectEmit(true, true, true, true);
        emit FeesCollectedFromVault(
            orderHash,
            expectedProtocolFee,
            expectedLpFee,
            expectedOperatorFee
        );

        vault.createOrder(order);
        vm.stopPrank();

        // Verify that fees were correctly accounted for
        assertEq(
            feeCollector.protocolFeesCollected(),
            expectedProtocolFee,
            "Protocol fees incorrect"
        );
        assertEq(
            feeCollector.lpFeesCollected(),
            expectedLpFee,
            "LP fees incorrect"
        );
        assertEq(
            feeCollector.operatorFeesCollected(),
            expectedOperatorFee,
            "Operator fees incorrect"
        );
    }
}
