{"rustc": 9905438375283332989, "features": "[\"custom\", \"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 11884987481660704207, "profile": 10243973527296709326, "path": 9702999646325421946, "deps": [[2452538001284770427, "cfg_if", false, 9829330037169451723]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-96cb7629881870f7\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "metadata": 12606519392706294666, "config": 2202906307356721367, "compile_kind": 0}