[profile.default]
src = "src"
out = "out"
libs = ["lib"]
gas_reports = ["*"]
cbor_metadata = true
optimizer = true
optimizer_runs = 200
etherscan_api_version = "v2"


[rpc_endpoints]
base = "${BASE_RPC_URL}"
sonic = "${SONIC_RPC_URL}"
polygon = "${POLYGON_RPC_URL}"
avax = "${AVAX_RPC_URL}"
arbitrum = "${ARBITRUM_RPC_URL}"
bsc = "${BSC_RPC_URL}"
optimism = "${OPTIMISM_RPC_URL}"
ethereum = "${ETHEREUM_RPC_URL}"

[etherscan]
optimism = { key = "${ETHERSCAN_API_KEY}" }
arbitrum = { key = "${ETHERSCAN_API_KEY}" }
ethereum = { key = "${ETHERSCAN_API_KEY}" }
sonic = { key = "${ETHERSCAN_API_KEY}" }
bsc = { key = "${ETHERSCAN_API_KEY}" }
base = { key = "${ETHERSCAN_API_KEY}" }
polygon = { key = "${ETHERSCAN_API_KEY}" }
avax = { key = "${ETHERSCAN_API_KEY}" }
