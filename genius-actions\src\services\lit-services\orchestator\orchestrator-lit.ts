// import { IOrchestrator } from './orchestrator.interface';

// export class OrchestratorLit implements IOrchestrator {
//     async getOrchestratorPrivateKey(ciphertext: string, dataToEncryptHash: string): Promise<string> {
//         const resp = await Lit.Actions.decryptAndCombine({
//             accessControlConditions,
//             ciphertext,
//             dataToEncryptHash,
//             authSig: null,
//             chain: 'ethereum',
//           });
//     }
// }
