use crate::*;

#[error_code]
pub enum GeniusError {
    #[msg("Admin address dismatch")]
    InvalidAdmin,

    #[msg("<PERSON><PERSON> can not be the orchestrator")]
    InvalidOrchestrator,

    #[msg("Orchestrator is not authorized")]
    IllegalOrchestrator,

    #[msg("Invalid withdraw amount")]
    InvalidAmount,

    #[msg("Need to rebalance")]
    NeedRebalance,

    #[msg("USDC balance should be increased after the swap")]
    SwapNotSuceed,

    #[msg("Instructions address is not correct")]
    InstructionsAddressMismatch,

    #[msg("Program address is not correct")]
    ProgramMismatch,

    #[msg("Instruction is unknown")]
    UnknownInstruction,

    #[msg("Incorrect repay address")]
    IncorrectRepay,

    #[msg("Can not borrow before repay")]
    CannotBorrowBeforeRepay,

    #[msg("Can not find repay instruction")]
    MissingRepay,

    #[msg("Order already exists")]
    OrderAlreadyExists,

    #[msg("Invalid order status")]
    InvalidOrderStatus,

    #[msg("Order deadline passed")]
    DeadlinePassed,

    #[msg("Order deadline not passed")]
    DeadlineNotPassed,

    #[msg("Authority already exists")]
    AuthorityAlreadyExists,

    #[msg("Authority does not exist")]
    AuthorityDoesNotExist,

    #[msg("Max authorities already set")]
    MaxAuthoritiesAlreadySet,

    #[msg("Invalid authority")]
    InvalidAuthority,

    #[msg("Global state is frozen")]
    GlobalStateFrozen,

    #[msg("Invalid order fill deadline")]
    InvalidOrderFillDeadline,

    #[msg("Invalid min amount out")]
    InvalidMinAmountOut,

    #[msg("The signer is not allowed to perform the given action")]
    UnauthorizedSigner,

    #[msg("Order not filled yet")]
    OrderNotFilled,

    #[msg("Invalid token out")]
    InvalidTokenOut,

    #[msg("Insufficient token out")]
    InsufficientTokenOut,

    #[msg("Insufficient fees")]
    InsufficientFees,

    #[msg("Stable coin price too low")]
    StableCoinPriceTooLow,

    #[msg("Stable coin price too high")]
    StableCoinPriceTooHigh,

    #[msg("Source and destination chain id should be different")]
    SameSourceAndDestinationChainIds,

    #[msg("Order amount should be greater than 0")]
    ZeroAmount,

    #[msg("Fee should be less than order amount")]
    ExcessFee,

    #[msg("Invalid trader for the given order")]
    InvalidTrader,

    #[msg("Token in should be same as stable coin")]
    InvalidTokenIn,

    #[msg("Order amount should be less than maximum order amount allowed")]
    MaxOrderAmountExceeded,

    #[msg("The length of the arrays should be greater than 0")]
    EmptyArray,

    #[msg("The length of the arrays: threshold_amounts and bps_fees should be the same")]
    FeeTiersLengthMismatched,

    #[msg("The length of the arrays: threshold_amounts and bps_fees should not be more than 10")]
    FeeTiersLengthExceeded,

    #[msg("The bps fee should be less than 10_000")]
    InvalidBpsFee,

    #[msg("The denominator should be greater than 0")]
    ZeroDenominator,

    #[msg("The length of the arrays: threshold_amounts and insurance_fees should be the same")]
    InsuranceFeeTiersLengthMismatched,

    #[msg("The length of the arrays: threshold_amounts and insurance_fees should not be more than 10")]
    InsuranceFeeTiersLengthExceeded,

    #[msg("The insurance fee should be less than 10_000")]
    InvalidInsuranceFee,

    #[msg("Orchestrator is not authorized to perform this action")]
    InvalidOrchestratorPermission,
}
