[submodule "lib/forge-std"]
	path = lib/forge-std
	url = https://github.com/foundry-rs/forge-std
[submodule "lib/openzeppelin-contracts"]
	path = lib/openzeppelin-contracts
	url = https://github.com/OpenZeppelin/openzeppelin-contracts
[submodule "lib/permit2"]
	path = lib/permit2
	url = https://github.com/Uniswap/permit2
[submodule "lib/solmate"]
	path = lib/solmate
	url = https://github.com/transmissions11/solmate
[submodule "lib/openzeppelin-contracts-upgradeable"]
	path = lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "lib/aave-v3-core"]
	path = lib/aave-v3-core
	url = https://github.com/aave/aave-v3-core
