{"rustc": 9905438375283332989, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 5499813885258112825, "profile": 18371596198776557358, "path": 15257486585390426430, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\winnow-d1e8abfd6ce2a231\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "metadata": 7482876514514569712, "config": 2202906307356721367, "compile_kind": 0}