[package]
name = "genius"
version = "0.1.0"
description = "Created with Anchor"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "genius"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []
idl-build = ["anchor-lang/idl-build", "anchor-spl/idl-build"]
test-bpf = []

[dependencies]
anchor-lang = { version = "0.30.1", features = ["init-if-needed"] }
anchor-spl = "0.30.1"
pyth-solana-receiver-sdk = "0.6.1"
pythnet-sdk = "=2.3.1"
solana-program = "1.17.3"

[profile.release]
overflow-checks = true
