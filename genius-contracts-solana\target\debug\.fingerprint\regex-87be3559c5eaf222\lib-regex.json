{"rustc": 9905438375283332989, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 10243973527296709326, "path": 10387215789119125289, "deps": [[554324495028472449, "memchr", false, 1324122977965919644], [6314779025451150414, "regex_automata", false, 8490942414065073766], [7325384046744447800, "aho_corasick", false, 7491749734835406557], [9111760993595911334, "regex_syntax", false, 3757860393022492015]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-87be3559c5eaf222\\dep-lib-regex", "checksum": false}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}