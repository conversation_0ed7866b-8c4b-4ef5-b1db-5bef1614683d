import { ChainId } from '../types/chain-id';

export const GENIUS_VAULT_ADR_DEV = (chainId: ChainId): string => {
  switch (chainId) {
    case ChainId.BASE:
      return '******************************************';
    case ChainId.OPTIMISM:
      return '******************************************';
    case ChainId.ARBITRUM:
      return '******************************************';
    case ChainId.BSC:
      return '******************************************';
    case ChainId.AVALANCHE:
      return '******************************************';
    case ChainId.ETHEREUM:
      return '******************************************';
    case ChainId.SONIC:
      return '******************************************';
    case ChainId.POLYGON:
      return '******************************************';
  }
  throw new Error(`No Genius Vault address found for chainId: ${chainId}`);
};

export const GENIUS_PROXY_CALL_ADR_DEV = (chainId: ChainId): string => {
  switch (chainId) {
    case ChainId.BASE:
      return '******************************************';
    case ChainId.OPTIMISM:
      return '******************************************';
    case ChainId.ARBITRUM:
      return '******************************************';
    case ChainId.BSC:
      return '******************************************';
    case ChainId.AVALANCHE:
      return '******************************************';
    case ChainId.ETHEREUM:
      return '******************************************';
    case ChainId.SONIC:
      return '******************************************';
    case ChainId.POLYGON:
      return '******************************************';
  }
  throw new Error(`No Genius Proxy Call address found for chainId: ${chainId}`);
};

export const STABLECOIN_ADR_DEV = (chainId: ChainId): string => {
  switch (chainId) {
    case ChainId.BASE:
      return '******************************************';
    case ChainId.OPTIMISM:
      return '******************************************';
    case ChainId.ARBITRUM:
      return '******************************************';
    case ChainId.BSC:
      return '******************************************';
    case ChainId.AVALANCHE:
      return '******************************************';
    case ChainId.SOLANA:
      return 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
    case ChainId.ETHEREUM:
      return '******************************************';
    case ChainId.SONIC:
      return '******************************************';
    case ChainId.POLYGON:
      return '******************************************';
  }
  throw new Error(`No stablecoin address found for chainId: ${chainId}`);
};

export const GENIUS_ACTIONS_DEV = {
  address: '******************************************',
  chain: ChainId.BASE,
};
