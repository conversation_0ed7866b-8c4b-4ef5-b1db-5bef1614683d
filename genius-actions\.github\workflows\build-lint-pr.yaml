name: Build on Pull Request

on:
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Use Node.js
      uses: actions/setup-node@v1
      with:
        node-version: '23.4.0'

    - name: Install dependencies
      run: yarn

    - name: Run Build EsBuild
      run: npm run build:esbuild

    - name: Run Build TSC
      run: npx tsc

    - name: Run Lint
      run: npm run lint