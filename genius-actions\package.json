{"name": "genius-actions", "version": "1.0.0", "main": "index.js", "scripts": {"build:esbuild": "node esbuild.config.js", "test": "npm run build:esbuild&&NODE_OPTIONS=\"$NODE_OPTIONS --experimental-vm-modules\" jest", "test:unit": "npm run build:esbuild&&jest --selectProjects=unit", "test:e2e": "npm run build:esbuild&&NODE_OPTIONS=\"$NODE_OPTIONS --experimental-vm-modules\" jest --selectProjects=e2e", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@lit-protocol/auth-helpers": "^7.0.4", "@lit-protocol/encryption": "^7.0.6", "@lit-protocol/lit-auth-client": "^7.0.4", "@lit-protocol/lit-node-client": "^7.0.4", "@lit-protocol/types": "^7.0.4", "@raydium-io/raydium-sdk": "^1.3.1-beta.58", "@raydium-io/raydium-sdk-v2": "^0.1.102-alpha", "@solana/spl-token": "^0.4.8", "@solana/web3.js": "^1.95.3", "axios": "^1.7.7", "bn.js": "^5.2.1", "bs58": "^6.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "dotenv": "^16.4.5", "esbuild": "^0.24.0", "ethers": "^5.7.2", "genius-intents": "^0.6.0", "pg": "^8.13.1", "stream-browserify": "^3.0.0", "ts-node": "^10.9.2", "typescript": "^5.6.3", "uninstall": "^0.0.0", "viem": "^2.22.12"}, "devDependencies": {"@types/bn.js": "^5.1.6", "@types/jest": "^29.5.14", "@types/pg": "^8.11.10", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.12.2", "eslint": "^8.42.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.5.3", "ts-jest": "^29.3.1"}}