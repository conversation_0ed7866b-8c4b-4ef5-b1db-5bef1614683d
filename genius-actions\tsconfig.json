{
  "compilerOptions": {
    "target": "es2021",
    "module": "NodeNext",
    "moduleResolution": "NodeNext", // Use NodeNext for better ESM support
    "outDir": "dist",
    "rootDir": "./",
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "noEmitOnError": false,
    "skipLibCheck": true,
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": false,
    "strict": true
  },
  "include": ["src/**/*", "scripts/**/*", "test/**/*"],
  "exclude": ["node_modules"]
}

