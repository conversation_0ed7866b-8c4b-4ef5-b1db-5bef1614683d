// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.4;

import {Test, console} from "forge-std/Test.sol";
import {GeniusProxyCall} from "../src/GeniusProxyCall.sol";
import {MockDEXRouter} from "./mocks/MockDEXRouter.sol";
import {IAllowanceTransfer} from "permit2/interfaces/IAllowanceTransfer.sol";
import {PermitSignature} from "./utils/SigUtils.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {IEIP712} from "permit2/interfaces/IEIP712.sol";
import {GeniusErrors} from "../src/libs/GeniusErrors.sol";
import {Pausable} from "@openzeppelin/contracts/utils/Pausable.sol";
import {GeniusRouter} from "../src/GeniusRouter.sol";
import {GeniusVault} from "../src/GeniusVault.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {IGeniusVault} from "../src/interfaces/IGeniusVault.sol";
import {IGeniusGasTank} from "../src/interfaces/IGeniusGasTank.sol";
import {GeniusGasTank} from "../src/GeniusGasTank.sol";
import {MockV3Aggregator} from "./mocks/MockV3Aggregator.sol";
import {FeeCollector} from "../src/fees/FeeCollector.sol";
import {IFeeCollector} from "../src/interfaces/IFeeCollector.sol";

contract GeniusSponsoredOrdersTest is Test {
    int256 public constant INITIAL_STABLECOIN_PRICE = 100_000_000;
    MockV3Aggregator public MOCK_PRICE_FEED;

    uint256 constant BASE_USER_WETH_BALANCE = 100 ether;
    uint256 constant BASE_USER_DAI_BALANCE = 100 ether;
    uint256 constant BASE_ROUTER_USDC_BALANCE = 100 ether;
    uint256 constant destChainId = 1;

    bytes32 public DOMAIN_SEPERATOR;

    uint256 avalanche;
    string private rpc = vm.envString("AVALANCHE_RPC_URL");

    IEIP712 public PERMIT2;
    PermitSignature public sigUtils;

    GeniusProxyCall public PROXYCALL;
    GeniusRouter public GENIUS_ROUTER;
    GeniusVault public GENIUS_VAULT;
    GeniusGasTank public GAS_TANK;
    FeeCollector public FEE_COLLECTOR;

    ERC20 public USDC;
    ERC20 public WETH;
    ERC20 public DAI;

    MockDEXRouter DEX_ROUTER;
    address ADMIN = makeAddr("ADMIN");
    address SENDER = makeAddr("SENDER");
    address FEE_RECIPIENT = makeAddr("FEE_RECIPIENT");

    address USER;
    uint256 USER_PK;

    bytes32 RECEIVER;
    bytes32 TOKEN_OUT;
    bytes32 TOKEN_IN;

    function setUp() public {
        avalanche = vm.createFork(rpc);

        vm.selectFork(avalanche);
        assertEq(vm.activeFork(), avalanche);

        (USER, USER_PK) = makeAddrAndKey("user");

        DEX_ROUTER = new MockDEXRouter();
        MOCK_PRICE_FEED = new MockV3Aggregator(INITIAL_STABLECOIN_PRICE);
        PERMIT2 = IEIP712(******************************************);
        DOMAIN_SEPERATOR = PERMIT2.DOMAIN_SEPARATOR();

        PROXYCALL = new GeniusProxyCall(ADMIN, new address[](0));

        USDC = ERC20(******************************************);
        WETH = ERC20(******************************************);
        DAI = ERC20(******************************************);
        sigUtils = new PermitSignature();

        vm.startPrank(ADMIN, ADMIN);

        GeniusVault implementation = new GeniusVault();

        bytes memory data = abi.encodeWithSelector(
            GeniusVault.initialize.selector,
            address(USDC),
            ADMIN,
            address(PROXYCALL),
            7_500,
            address(MOCK_PRICE_FEED),
            86_000,
            99_000_000,
            101_000_000,
            1000 ether
        );

        ERC1967Proxy proxy = new ERC1967Proxy(address(implementation), data);

        GENIUS_VAULT = GeniusVault(address(proxy));

        // Deploy FeeCollector
        FeeCollector feeCollectorImplementation = new FeeCollector();

        bytes memory feeCollectorData = abi.encodeWithSelector(
            FeeCollector.initialize.selector,
            ADMIN,
            address(USDC),
            2000, // 20% to protocol,
            ADMIN,
            ADMIN,
            ADMIN
        );

        ERC1967Proxy feeCollectorProxy = new ERC1967Proxy(
            address(feeCollectorImplementation),
            feeCollectorData
        );

        FEE_COLLECTOR = FeeCollector(address(feeCollectorProxy));

        // Set FeeCollector in vault
        GENIUS_VAULT.setFeeCollector(address(FEE_COLLECTOR));

        // Set vault in FeeCollector
        FEE_COLLECTOR.setVault(address(GENIUS_VAULT));

        // Set up fee tiers in FeeCollector
        uint256[] memory thresholdAmounts = new uint256[](3);
        thresholdAmounts[0] = 0;
        thresholdAmounts[1] = 100 ether;
        thresholdAmounts[2] = 500 ether;

        uint256[] memory bpsFees = new uint256[](3);
        bpsFees[0] = 30; // 0.3%
        bpsFees[1] = 20; // 0.2%
        bpsFees[2] = 10; // 0.1%

        FEE_COLLECTOR.setFeeTiers(thresholdAmounts, bpsFees);

        // Set up insurance fee tiers
        uint256[] memory insThresholdAmounts = new uint256[](3);
        insThresholdAmounts[0] = 0;
        insThresholdAmounts[1] = 100 ether;
        insThresholdAmounts[2] = 500 ether;

        uint256[] memory insBpsFees = new uint256[](3);
        insBpsFees[0] = 30; // 0.3%
        insBpsFees[1] = 20; // 0.2%
        insBpsFees[2] = 10; // 0.1%

        FEE_COLLECTOR.setInsuranceFeeTiers(insThresholdAmounts, insBpsFees);

        // Set min fee in FeeCollector
        FEE_COLLECTOR.setTargetChainMinFee(destChainId, 1 ether);

        // Set decimals in Vault
        GENIUS_VAULT.setChainStablecoinDecimals(destChainId, 6);
        GENIUS_ROUTER = new GeniusRouter(
            address(PERMIT2),
            address(GENIUS_VAULT),
            address(PROXYCALL),
            address(FEE_COLLECTOR)
        );

        RECEIVER = GENIUS_VAULT.addressToBytes32(USER);
        TOKEN_OUT = GENIUS_VAULT.addressToBytes32(address(USDC));
        TOKEN_IN = TOKEN_OUT;

        GAS_TANK = new GeniusGasTank(
            ADMIN,
            payable(FEE_RECIPIENT),
            address(PERMIT2),
            address(PROXYCALL)
        );

        vm.startPrank(ADMIN);

        PROXYCALL.grantRole(PROXYCALL.CALLER_ROLE(), address(GENIUS_ROUTER));
        PROXYCALL.grantRole(PROXYCALL.CALLER_ROLE(), address(GENIUS_VAULT));
        PROXYCALL.grantRole(PROXYCALL.CALLER_ROLE(), address(GAS_TANK));
        GENIUS_VAULT.setChainStablecoinDecimals(destChainId, 6);

        vm.stopPrank();

        deal(address(USDC), address(DEX_ROUTER), BASE_ROUTER_USDC_BALANCE);
        deal(address(DAI), USER, BASE_USER_DAI_BALANCE);
        deal(address(WETH), USER, BASE_USER_WETH_BALANCE);

        vm.startPrank(USER);
        USDC.approve(address(PERMIT2), type(uint256).max);
        DAI.approve(address(PERMIT2), type(uint256).max);
        vm.stopPrank();
    }

    function testSponsorOrderCreation() public {
        uint256 sponsorFee = 1.3 ether; // Updated to match new fee calculation

        address[] memory tokensIn = new address[](1);
        uint256[] memory amountsIn = new uint256[](1);

        tokensIn[0] = address(DAI);
        amountsIn[0] = BASE_USER_DAI_BALANCE - sponsorFee;

        bytes memory data = abi.encodeWithSelector(
            DEX_ROUTER.swapTo.selector,
            address(DAI),
            address(USDC),
            BASE_USER_DAI_BALANCE - sponsorFee,
            address(GENIUS_ROUTER)
        );

        uint256 bridgeFee = FEE_COLLECTOR
            .getOrderFees(BASE_ROUTER_USDC_BALANCE / 2, destChainId)
            .totalFee;
        uint256 minAmountOut = 49 ether;

        bytes memory gasTankData = abi.encodeWithSelector(
            GENIUS_ROUTER.swapAndCreateOrder.selector,
            bytes32(uint256(1)),
            tokensIn,
            amountsIn,
            address(DEX_ROUTER),
            address(DEX_ROUTER),
            data,
            USER,
            destChainId,
            0, // feeSurplus
            RECEIVER,
            minAmountOut,
            TOKEN_OUT
        );

        IAllowanceTransfer.PermitDetails memory details = IAllowanceTransfer
            .PermitDetails({
                token: address(DAI),
                amount: uint160(BASE_USER_DAI_BALANCE),
                nonce: 0,
                expiration: 1900000000
            });

        IAllowanceTransfer.PermitDetails[]
            memory detailsArray = new IAllowanceTransfer.PermitDetails[](1);

        detailsArray[0] = details;

        (
            IAllowanceTransfer.PermitBatch memory permitBatch,
            bytes memory permitSignature
        ) = _generatePermitBatchSignature(detailsArray);

        bytes memory sponsorSignature = _generateSignature(
            address(GENIUS_ROUTER),
            gasTankData,
            permitBatch,
            GAS_TANK.nonces(USER),
            address(DAI),
            sponsorFee,
            1900000000,
            USER_PK
        );

        IGeniusVault.Order memory order = IGeniusVault.Order({
            seed: bytes32(uint256(1)),
            trader: RECEIVER,
            receiver: RECEIVER,
            tokenIn: TOKEN_IN,
            tokenOut: TOKEN_OUT,
            amountIn: BASE_ROUTER_USDC_BALANCE / 2,
            minAmountOut: minAmountOut,
            destChainId: destChainId,
            srcChainId: block.chainid,
            fee: bridgeFee
        });

        vm.startPrank(SENDER);

        vm.expectEmit(address(GENIUS_VAULT));
        emit IGeniusVault.OrderCreated(
            destChainId,
            RECEIVER,
            RECEIVER,
            bytes32(uint256(1)),
            GENIUS_VAULT.orderHash(order),
            TOKEN_IN,
            TOKEN_OUT,
            BASE_ROUTER_USDC_BALANCE / 2,
            minAmountOut,
            bridgeFee
        );

        vm.expectEmit(address(GAS_TANK));
        emit IGeniusGasTank.OrderedTransactionsSponsored(
            SENDER,
            USER,
            address(GENIUS_ROUTER),
            address(DAI),
            sponsorFee,
            0
        );

        GAS_TANK.sponsorOrderedTransactions(
            address(GENIUS_ROUTER),
            gasTankData,
            permitBatch,
            permitSignature,
            USER,
            address(DAI),
            sponsorFee,
            1900000000,
            sponsorSignature
        );

        vm.stopPrank();

        IFeeCollector.FeeBreakdown memory fees = FEE_COLLECTOR.getOrderFees(
            BASE_ROUTER_USDC_BALANCE / 2,
            destChainId
        );

        assertEq(
            USDC.balanceOf(address(GENIUS_VAULT)),
            BASE_ROUTER_USDC_BALANCE / 2 + fees.insuranceFee - fees.totalFee
        );
    }

    function _generatePermitBatchSignature(
        IAllowanceTransfer.PermitDetails[] memory details
    )
        internal
        view
        returns (IAllowanceTransfer.PermitBatch memory, bytes memory)
    {
        IAllowanceTransfer.PermitBatch memory permitBatch = IAllowanceTransfer
            .PermitBatch({
                details: details,
                spender: address(GAS_TANK),
                sigDeadline: 1900000000
            });

        bytes memory permitSignature = sigUtils.getPermitBatchSignature(
            permitBatch,
            USER_PK,
            DOMAIN_SEPERATOR
        );

        return (permitBatch, permitSignature);
    }

    function _generateSignature(
        address target,
        bytes memory data,
        IAllowanceTransfer.PermitBatch memory permitBatch,
        uint256 nonce,
        address feeToken,
        uint256 feeAmount,
        uint256 deadline,
        uint256 privateKey
    ) internal view returns (bytes memory) {
        bytes32 messageHash = keccak256(
            abi.encode(
                block.chainid,
                target,
                data,
                permitBatch,
                nonce,
                feeToken,
                feeAmount,
                deadline,
                address(GAS_TANK)
            )
        );

        bytes32 ethSignedMessageHash = keccak256(
            abi.encodePacked("\x19Ethereum Signed Message:\n32", messageHash)
        );
        (uint8 v, bytes32 r, bytes32 s) = vm.sign(
            privateKey,
            ethSignedMessageHash
        );
        return abi.encodePacked(r, s, v);
    }
}
